<template>
  <div class="p-4">
    <div class="bg-white p-4 rounded-lg shadow-sm">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-medium">机柜管理</h2>
        <div class="flex gap-2">
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <PlusOutlined />
            </template>
            新增机柜
          </a-button>
          <a-button @click="handleRefresh" :loading="loading">
            <template #icon>
              <SyncOutlined />
            </template>
            刷新
          </a-button>
        </div>
      </div>

      <!-- 搜索表单 -->
      <div class="mb-4">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch" class="search-form">
          <a-form-item label="机柜名称">
            <a-input v-model:value="searchForm.name" placeholder="请输入机柜名称" allow-clear />
          </a-form-item>
          <a-form-item label="房间">
            <a-select v-model:value="searchForm.roomId" placeholder="请选择房间" class="w-50" show-search allow-clear :filter-option="filterOption">
              <a-select-option v-for="room in roomOptions" :key="room.value" :value="room.value">
                {{ room.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" html-type="submit">查询</a-button>
            <a-button @click="handleReset" class="ml-2">重置</a-button>
          </a-form-item>
        </a-form>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
        size="middle"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        :scroll="{ x: 1000 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'totalU'">
            <div class="flex items-center justify-center cabinet-u-info">
              <a-tag color="blue" class="px-2 py-1 text-sm font-medium"> {{ record.totalU || 0 }}U </a-tag>
            </div>
          </template>
          <template v-if="column.key === 'usage'">
            <div class="flex flex-col gap-1 usage-progress">
              <a-progress :percent="calculateUsagePercent(record)" :status="getUsageStatus(record)" size="small" />
              <div class="flex justify-between text-xs text-gray-500">
                <span>已用: {{ record.useU || 0 }}U</span>
                <span>总计: {{ record.totalU || 0 }}U</span>
              </div>
            </div>
          </template>
          <template v-if="column.key === 'useU'">
            <div class="flex items-center justify-center cabinet-u-info">
              <a-tag :color="getUsedUColor(record)" class="px-2 py-1 text-sm font-medium"> {{ record.useU || 0 }}U </a-tag>
            </div>
          </template>
          <template v-if="column.key === 'unuseU'">
            <div class="flex items-center justify-center cabinet-u-info">
              <a-tooltip :title="formatUnusedUTooltip(record.unuseU)" placement="top">
                <a-tag color="green" class="px-2 py-1 text-sm font-medium"> {{ getUnusedUCount(record) }}U </a-tag>
              </a-tooltip>
            </div>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="showCabinetDetail(record)">查看详情</a-button>
              <a-button type="link" size="small" @click="handleBindDevice(record)">绑定设备</a-button>
              <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
              <a-popconfirm title="确定要删除这个机柜吗？" @confirm="handleDelete([record.id])">
                <a-button type="link" size="small" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>

      <!-- 批量操作 -->
      <div v-if="selectedRowKeys.length > 0" class="mt-4">
        <a-space>
          <span>已选择 {{ selectedRowKeys.length }} 项</span>
          <a-popconfirm title="确定要删除选中的机柜吗？" @confirm="handleDelete(selectedRowKeys)">
            <a-button type="primary" danger size="small">批量删除</a-button>
          </a-popconfirm>
        </a-space>
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <BasicModal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑机柜' : '新增机柜'"
      @ok="handleSubmit"
      @cancel="handleCancel"
      :confirm-loading="submitLoading"
      width="700px"
      class="cabinet-modal"
    >
      <BasicForm @register="registerCabinetForm" />
    </BasicModal>

    <!-- 绑定设备弹窗 -->
    <BasicModal
      v-model:open="bindModalVisible"
      title="绑定设备到机柜"
      @ok="handleBindSubmit"
      @cancel="handleBindCancel"
      :confirm-loading="bindSubmitLoading"
      width="600px"
      class="bind-modal"
    >
      <BasicForm @register="registerBindForm" />
    </BasicModal>

    <!-- 机柜详情弹窗 -->
    <BasicModal
      v-model:open="cabinetDetailVisible"
      :title="`机柜详情 - ${currentCabinetDetail?.name || ''}`"
      @cancel="handleCabinetDetailCancel"
      width="800px"
      class="cabinet-detail-modal"
      :footer="null"
    >
      <div v-if="currentCabinetDetail" class="cabinet-detail-content">
        <!-- 机柜信息 -->
        <div class="cabinet-info mb-4">
          <div class="info-grid">
            <div class="info-item">
              <span class="label">机柜名称：</span>
              <span class="value">{{ currentCabinetDetail.name }}</span>
            </div>
          </div>
        </div>

        <!-- 机柜可视化和设备信息 -->
        <div class="cabinet-layout">
          <!-- 绑定设备信息 -->
          <div class="cabinet-devices">
            <div class="devices-header">
              <h4 class="devices-title">绑定设备 ({{ cabinetDevices.length }}台)</h4>
            </div>

            <div v-if="cabinetDetailLoading" class="devices-loading">
              <a-spin size="small" />
              <span class="ml-2">加载设备信息中...</span>
            </div>

            <div v-else-if="cabinetDevices.length === 0" class="devices-empty">
              <a-empty :image="false" description="暂无绑定设备" />
            </div>

            <div v-else class="devices-summary">
              <a-alert
                message="设备管理提示"
                :description="`当前机柜共有 ${cabinetDevices.length} 台设备。您可以在右侧的机柜分布图中查看详细信息，并直接在设备块上进行解绑操作。`"
                type="info"
                show-icon
                closable
              />
            </div>
          </div>

          <!-- 机柜可视化 -->
          <div class="cabinet-visual">
            <h4 class="visual-title">机柜U位分布图</h4>
            <div class="cabinet-container">
              <div class="cabinet-header">
                <div class="cabinet-title">{{ currentCabinetDetail.name }}</div>
                <div class="cabinet-brand">RACK</div>
              </div>
              <div class="cabinet-body">
                <div class="u-slots">
                  <a-tooltip
                    v-for="(block, index) in generateDeviceBlocks(currentCabinetDetail)"
                    :key="index"
                    :title="
                      block.device
                        ? `${block.device.deviceName}${block.device.models ? ` (${block.device.models})` : ''} - U${block.startU}${block.height > 1 ? `-${block.endU}` : ''}`
                        : block.type === 'available'
                          ? '可用U位'
                          : '未知状态'
                    "
                    placement="left"
                  >
                    <div class="device-block" :class="block.type" :style="{ height: `${block.height * 24 + (block.height - 1)}px` }">
                      <div class="block-content">
                        <!-- 合并设备的内部分割线 -->
                        <div v-if="block.device && block.height > 1" class="u-dividers">
                          <div
                            v-for="dividerIndex in block.height - 1"
                            :key="dividerIndex"
                            class="u-divider"
                            :style="{ top: `${dividerIndex * 24}px` }"
                          ></div>
                        </div>

                        <div class="block-info">
                          <div class="u-range">U{{ block.startU }}{{ block.height > 1 ? `-${block.endU}` : '' }}</div>
                          <div v-if="block.device" class="device-name">{{ block.device.deviceName }}</div>
                          <div v-else-if="block.type === 'available'" class="status-text">可用</div>
                          <div v-else class="status-text">未知</div>
                        </div>
                        <div v-if="block.device" class="block-actions">
                          <a-popconfirm
                            :title="`确定要解绑设备 ${block.device.deviceName} 吗？`"
                            @confirm="handleUnbindDeviceFromBlock(block.device!)"
                            placement="left"
                          >
                            <a-button type="link" size="small" danger class="unbind-btn">
                              <template #icon>
                                <DeleteOutlined />
                              </template>
                            </a-button>
                          </a-popconfirm>
                        </div>
                      </div>
                    </div>
                  </a-tooltip>
                </div>
              </div>
              <div class="cabinet-footer">
                <div class="cabinet-logo">DCIM</div>
              </div>
            </div>

            <!-- 图例 -->
            <div class="legend">
              <div class="legend-item">
                <div class="legend-color used"></div>
                <span>已使用</span>
              </div>
              <div class="legend-item">
                <div class="legend-color available"></div>
                <span>可使用</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </BasicModal>
  </div>
</template>

<style scoped lang="less">
  .cabinet-u-info {
    .ant-tag {
      border-radius: 6px;
      border: none;
      font-weight: 500;
      text-align: center;
      min-width: 45px;
    }
  }

  .usage-progress {
    .ant-progress-line {
      .ant-progress-bg {
        height: 6px !important;
        border-radius: 3px;
      }
    }
  }

  .u-detail-btn {
    transition: all 0.2s ease;
    &:hover {
      transform: translateY(-1px);
    }
  }

  // 让表格更紧凑美观
  :deep(.ant-table-tbody) {
    .ant-table-cell {
      padding: 12px 8px;
    }
  }

  :deep(.ant-progress-text) {
    font-size: 12px;
    font-weight: 500;
  }

  // 机柜详情弹窗样式
  .cabinet-detail-content {
    padding: 10px 0;

    .cabinet-info {
      background: #f8f9fa;
      padding: 16px;
      border-radius: 8px;
      margin-bottom: 20px;

      .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px 16px;

        .info-item {
          display: flex;
          align-items: center;
          font-size: 14px;

          .label {
            font-weight: 500;
            color: #666;
            min-width: 70px;
            flex-shrink: 0;
          }

          .value {
            color: #333;
            font-weight: 600;
            flex: 1;
          }
        }
      }
    }

    .cabinet-devices {
      background: #fff;
      border: 1px solid #e8e8e8;
      border-radius: 8px;
      overflow: hidden;

      .devices-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        background: #fafafa;
        border-bottom: 1px solid #e8e8e8;

        .devices-title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }

        .devices-actions {
          .ant-btn {
            border-radius: 6px;
          }
        }
      }

      .devices-loading {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 32px;
        color: #999;
      }

      .devices-empty {
        padding: 24px;
        text-align: center;
      }

      .devices-summary {
        padding: 16px 20px;

        :deep(.ant-alert) {
          border-radius: 8px;

          .ant-alert-message {
            font-weight: 600;
            color: #1890ff;
          }

          .ant-alert-description {
            color: #666;
            margin-top: 4px;
          }
        }
      }
    }

    .cabinet-layout {
      display: flex;
      gap: 24px;
      align-items: flex-start;

      .cabinet-devices {
        flex: 0 0 300px;
        min-height: 200px;
      }

      .cabinet-visual {
        flex: 1;
        min-width: 350px;
      }
    }

    @media (max-width: 768px) {
      .cabinet-layout {
        flex-direction: column;
        gap: 16px;

        .cabinet-devices {
          flex: none;
          width: 100%;
        }
      }
    }

    .cabinet-visual {
      .visual-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 16px;
        text-align: center;
      }

      .cabinet-container {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        border-radius: 12px;
        padding: 16px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        margin: 0 auto 16px;
        max-width: 350px;

        .cabinet-header {
          background: #1a252f;
          color: white;
          padding: 8px 12px;
          border-radius: 6px 6px 0 0;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2px;

          .cabinet-title {
            font-size: 12px;
            font-weight: 600;
          }

          .cabinet-brand {
            font-size: 10px;
            color: #bdc3c7;
            font-family: monospace;
          }
        }

        .cabinet-body {
          background: #2c3e50;
          border-radius: 0;
          padding: 4px;

          .u-slots {
            display: flex;
            flex-direction: column-reverse;
            gap: 1px;
            max-height: 300px;
            overflow-y: auto;
            padding-right: 4px;

            &::-webkit-scrollbar {
              width: 4px;
            }

            &::-webkit-scrollbar-track {
              background: #34495e;
              border-radius: 2px;
            }

            &::-webkit-scrollbar-thumb {
              background: #7f8c8d;
              border-radius: 2px;
            }

            .device-block {
              position: relative;
              border-radius: 4px;
              border: 1px solid transparent;
              transition: all 0.2s ease;
              cursor: pointer;
              min-height: 24px;

              &:hover {
                transform: translateX(2px);
                box-shadow: 2px 0 8px rgba(0, 0, 0, 0.2);
                z-index: 10;

                .block-actions {
                  opacity: 1;
                }
              }

              .block-content {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: space-between;
                height: 100%;
                padding: 4px 8px;

                .u-dividers {
                  position: absolute;
                  left: 0;
                  right: 0;
                  top: 0;
                  height: 100%;
                  pointer-events: none;
                  z-index: 1;

                  .u-divider {
                    position: absolute;
                    left: 8px;
                    right: 8px;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.3);
                    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

                    &::before {
                      content: '';
                      position: absolute;
                      left: 0;
                      right: 0;
                      height: 1px;
                      background: rgba(0, 0, 0, 0.1);
                      top: 1px;
                    }
                  }
                }

                .block-info {
                  flex: 1;
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  min-height: 24px;
                  position: relative;
                  z-index: 2;

                  .u-range {
                    font-family: monospace;
                    font-weight: 600;
                    font-size: 10px;
                    line-height: 1.2;
                  }

                  .device-name {
                    font-size: 9px;
                    font-weight: 500;
                    opacity: 0.9;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    max-width: 120px;
                    line-height: 1.2;
                  }

                  .status-text {
                    font-size: 10px;
                    font-weight: 500;
                    opacity: 0.9;
                    line-height: 1.2;
                  }
                }

                .block-actions {
                  opacity: 0;
                  transition: opacity 0.2s ease;
                  position: absolute;
                  right: 4px;
                  top: 50%;
                  transform: translateY(-50%);
                  z-index: 3;

                  .unbind-btn {
                    padding: 0;
                    width: 16px;
                    height: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 2px;
                    background: rgba(255, 255, 255, 0.2);

                    :deep(.anticon) {
                      font-size: 10px;
                    }

                    &:hover {
                      background: rgba(255, 77, 79, 0.8);
                      color: white !important;
                    }
                  }
                }
              }

              &.used {
                background: linear-gradient(90deg, #e74c3c, #c0392b);
                color: white;
                border-color: #c0392b;
              }

              &.available {
                background: linear-gradient(90deg, #27ae60, #229954);
                color: white;
                border-color: #229954;
              }

              &.unknown {
                background: linear-gradient(90deg, #95a5a6, #7f8c8d);
                color: white;
                border-color: #7f8c8d;
              }
            }
          }
        }

        .cabinet-footer {
          background: #1a252f;
          color: #bdc3c7;
          padding: 6px 12px;
          border-radius: 0 0 6px 6px;
          text-align: center;
          margin-top: 2px;

          .cabinet-logo {
            font-size: 10px;
            font-family: monospace;
            font-weight: 600;
          }
        }
      }

      .legend {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-top: 16px;

        .legend-item {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 12px;
          color: #666;

          .legend-color {
            width: 14px;
            height: 14px;
            border-radius: 3px;
            border: 1px solid #ddd;

            &.used {
              background: linear-gradient(90deg, #e74c3c, #c0392b);
            }

            &.available {
              background: linear-gradient(90deg, #27ae60, #229954);
            }
          }
        }
      }
    }
  }

  .search-form {
    :deep(.ant-form-item) {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      margin-right: 16px;

      .ant-form-item-label {
        flex: none;
        width: auto;
        margin-right: 8px;

        label {
          white-space: nowrap;
        }
      }

      .ant-form-item-control {
        flex: 1;
        min-width: 0;
      }
    }
  }

  :deep(.cabinet-modal) {
    .ant-modal-body {
      padding: 24px;
      background: linear-gradient(135deg, #f6f9fc 0%, #e9f4ff 100%);
      border-radius: 8px;
    }

    .ant-form {
      .ant-form-item {
        margin-bottom: 20px;
        background: rgba(255, 255, 255, 0.9);
        padding: 16px;
        border-radius: 10px;
        backdrop-filter: blur(8px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
          background: rgba(255, 255, 255, 0.95);
        }

        .ant-form-item-label {
          padding-bottom: 0;
          padding-right: 12px;
          text-align: right;
          width: 100px !important;
          min-width: 100px !important;
          flex: none;

          label {
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            font-size: 14px;
            position: relative;
            display: inline-block;
            white-space: nowrap;
            width: 100%;

            &::before {
              content: '';
              position: absolute;
              bottom: -3px;
              left: 0;
              width: 0;
              height: 2px;
              background: linear-gradient(90deg, #1890ff, #40a9ff);
              transition: width 0.3s ease;
            }

            &:hover::before {
              width: 100%;
            }
          }
        }

        .ant-form-item-control {
          flex: 1;

          .ant-form-item-control-input {
            min-height: 32px;

            .ant-input,
            .ant-input-number,
            .ant-select-selector {
              border-radius: 6px;
              transition: all 0.2s;
              border: 1px solid #d9d9d9;
              background: rgba(255, 255, 255, 0.95);

              &:hover {
                border-color: #40a9ff;
                box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
              }

              &:focus,
              &.ant-select-focused .ant-select-selector {
                border-color: #40a9ff;
                box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
              }
            }

            .ant-input-number {
              width: 100%;
            }
          }
        }

        &.ant-form-item-required {
          border-left: 4px solid #1890ff;

          .ant-form-item-label label {
            &::after {
              content: ' *';
              color: #ff4d4f;
              font-weight: bold;
            }
          }
        }

        &.ant-form-item-has-error {
          border-left: 4px solid #ff4d4f;
          background: rgba(255, 77, 79, 0.05);

          .ant-form-item-explain {
            margin-top: 4px;
            font-size: 12px;
            color: #ff4d4f;
          }

          .ant-form-item-control-input {
            .ant-input,
            .ant-input-number,
            .ant-select-selector {
              border-color: #ff4d4f;
              box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
            }
          }
        }

        &.ant-form-item-has-success {
          border-left: 4px solid #52c41a;

          .ant-form-item-control-input {
            .ant-input,
            .ant-input-number,
            .ant-select-selector {
              border-color: #52c41a;
            }
          }
        }
      }
    }
  }

  // 绑定设备弹窗样式
  :deep(.bind-modal) {
    .ant-modal-body {
      padding: 24px;
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border-radius: 8px;
    }

    .ant-form {
      .ant-form-item {
        margin-bottom: 20px;
        background: rgba(255, 255, 255, 0.9);
        padding: 16px;
        border-radius: 10px;
        backdrop-filter: blur(8px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
          background: rgba(255, 255, 255, 0.95);
        }

        .ant-form-item-label {
          padding-bottom: 0;
          padding-right: 12px;
          text-align: right;
          width: 100px !important;
          min-width: 100px !important;
          flex: none;

          label {
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            font-size: 14px;
            position: relative;
            display: inline-block;
            white-space: nowrap;
            width: 100%;

            &::before {
              content: '';
              position: absolute;
              bottom: -3px;
              left: 0;
              width: 0;
              height: 2px;
              background: linear-gradient(90deg, #0ea5e9, #38bdf8);
              transition: width 0.3s ease;
            }

            &:hover::before {
              width: 100%;
            }
          }
        }

        .ant-form-item-control {
          flex: 1;

          .ant-form-item-control-input {
            min-height: 32px;

            .ant-input,
            .ant-input-number,
            .ant-select-selector {
              border-radius: 6px;
              transition: all 0.2s;
              border: 1px solid #d9d9d9;
              background: rgba(255, 255, 255, 0.95);

              &:hover {
                border-color: #0ea5e9;
                box-shadow: 0 2px 4px rgba(14, 165, 233, 0.1);
              }

              &:focus,
              &.ant-select-focused .ant-select-selector {
                border-color: #0ea5e9;
                box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.2);
              }
            }

            .ant-input-number {
              width: 100%;
            }
          }
        }

        &.ant-form-item-required {
          border-left: 4px solid #0ea5e9;

          .ant-form-item-label label {
            &::after {
              content: ' *';
              color: #f59e0b;
              font-weight: bold;
            }
          }
        }

        &.ant-form-item-has-error {
          border-left: 4px solid #ef4444;
          background: rgba(239, 68, 68, 0.05);

          .ant-form-item-explain {
            margin-top: 4px;
            font-size: 12px;
            color: #ef4444;
          }

          .ant-form-item-control-input {
            .ant-input,
            .ant-input-number,
            .ant-select-selector {
              border-color: #ef4444;
              box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
            }
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    :deep(.cabinet-modal),
    :deep(.bind-modal) {
      .ant-modal {
        margin: 16px;
        max-width: calc(100vw - 32px);
      }

      .ant-form {
        .ant-form-item {
          display: block !important;

          .ant-form-item-label {
            text-align: left !important;
            padding-right: 0 !important;
            padding-bottom: 8px !important;
            width: auto !important;
            min-width: auto !important;
          }

          .ant-form-item-control {
            flex: none !important;
          }
        }
      }
    }
  }
</style>

<script setup lang="ts">
  import { ref, reactive, onMounted, nextTick, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { PlusOutlined, SyncOutlined, DeleteOutlined } from '@ant-design/icons-vue';
  import {
    getAssetCabinetList,
    getAssetCabinetById,
    addAssetCabinet,
    updateAssetCabinet,
    deleteAssetCabinet,
    bindDeviceToCabinet,
    getCabinetDevicesByCabinetId,
    unbindCabinetDevices,
    type AssetCabinetModel,
    type AssetCabinetParams,
    type CabinetBindDeviceParams,
    type CabinetDeviceInfo,
    type UnbindDeviceParams,
  } from '/@/api/asset/cabinet';
  import { getRoomList, type RoomModel } from '/@/api/asset/room';
  import { getAssetDeviceList, type AssetDeviceModel } from '/@/api/asset/device';
  import { BasicModal } from '/@/components/Modal';
  import { BasicForm, useForm, FormSchema } from '/@/components/Form';
  import { safeSetFieldsValue } from '/@/utils/form';

  const DEFAULT_PAGE_SIZE = 10;

  interface SearchForm {
    name: string;
    roomId: number | undefined;
  }

  interface ExtendedAssetCabinetModel extends AssetCabinetModel {
    roomName?: string;
  }

  const loading = ref<boolean>(false);
  const submitLoading = ref<boolean>(false);
  const tableData = ref<ExtendedAssetCabinetModel[]>([]);
  const selectedRowKeys = ref<number[]>([]);
  const modalVisible = ref<boolean>(false);
  const isEdit = ref<boolean>(false);
  const roomOptions = ref<{ label: string; value: number }[]>([]);

  // 绑定设备相关
  const bindModalVisible = ref<boolean>(false);
  const bindSubmitLoading = ref<boolean>(false);
  const deviceOptions = ref<{ label: string; value: number }[]>([]);
  const currentCabinet = ref<ExtendedAssetCabinetModel | null>(null);

  // 机柜详情相关
  const cabinetDetailVisible = ref<boolean>(false);
  const currentCabinetDetail = ref<ExtendedAssetCabinetModel | null>(null);
  const cabinetDevices = ref<CabinetDeviceInfo[]>([]);
  const cabinetDetailLoading = ref<boolean>(false);

  const searchForm = reactive<SearchForm>({
    name: '',
    roomId: undefined,
  });

  const cabinetFormSchema: FormSchema[] = [
    {
      label: '',
      field: 'id',
      component: 'Input',
      show: false,
    },
    {
      label: '机柜名称',
      field: 'name',
      required: true,
      component: 'Input',
      componentProps: {
        placeholder: '请输入机柜名称',
        maxlength: 50,
      },
      colProps: { span: 12 },
      rules: [
        { required: true, message: '请输入机柜名称' },
        { min: 1, max: 50, message: '机柜名称长度应在1-50个字符之间' },
      ],
    },
    {
      label: '机柜编号',
      field: 'code',
      component: 'Input',
      componentProps: {
        placeholder: '请输入机柜编号',
        maxlength: 50,
      },
      colProps: { span: 12 },
      rules: [{ min: 1, max: 50, message: '机柜编号长度应在1-50个字符之间' }],
    },
    {
      label: '房间',
      field: 'roomId',
      required: true,
      component: 'Select',
      componentProps: () => ({
        placeholder: '请选择房间',
        class: 'w-full',
        options: roomOptions.value,
        showSearch: true,
        filterOption: (input: string, option: any) => {
          return option?.label?.toLowerCase().includes(input.toLowerCase());
        },
      }),
      colProps: { span: 24 },
      rules: [{ required: true, message: '请选择房间' }],
    },
    {
      label: '总U位',
      field: 'totalU',
      required: true,
      component: 'InputNumber',
      componentProps: {
        min: 1,
        max: 100,
        placeholder: '请输入总U位',
        class: 'w-full',
      },
      colProps: { span: 24 },
      rules: [
        { required: true, message: '请输入总U位' },
        { type: 'number', min: 1, max: 100, message: '总U位应在1-100之间' },
      ],
    },
    {
      label: '备注',
      field: 'remark',
      component: 'InputTextArea',
      componentProps: {
        placeholder: '请输入备注',
        rows: 3,
        maxlength: 500,
      },
      colProps: { span: 24 },
      rules: [{ max: 500, message: '备注长度不能超过500个字符' }],
    },
  ];

  const [registerCabinetForm, { resetFields, setFieldsValue, validate }] = useForm({
    schemas: cabinetFormSchema,
    showActionButtonGroup: false,
    labelCol: {
      xs: { span: 24 },
      sm: { span: 6 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 18 },
    },
    baseColProps: { span: 24 },
    labelWidth: 100,
  });

  // 绑定设备表单配置
  const bindFormSchema: FormSchema[] = [
    {
      label: '机柜名称',
      field: 'cabinetName',
      component: 'Input',
      componentProps: {
        disabled: true,
      },
      colProps: { span: 24 },
    },
    {
      label: '选择设备',
      field: 'assDeviceId',
      required: true,
      component: 'Select',
      componentProps: () => ({
        placeholder: '请选择要绑定的设备',
        class: 'w-full',
        options: deviceOptions.value,
        showSearch: true,
        filterOption: (input: string, option: any) => {
          return option?.label?.toLowerCase().includes(input.toLowerCase());
        },
      }),
      colProps: { span: 24 },
      rules: [{ required: true, message: '请选择要绑定的设备' }],
    },
    {
      label: '起始U位',
      field: 'startU',
      required: true,
      component: 'InputNumber',
      componentProps: {
        min: 1,
        placeholder: '请输入起始U位',
        class: 'w-full',
      },
      colProps: { span: 24 },
      rules: [
        { required: true, message: '请输入起始U位' },
        { type: 'number', min: 1, message: '起始U位必须大于0' },
      ],
    },
  ];

  const [registerBindForm, { resetFields: resetBindFields, setFieldsValue: setBindFieldsValue, validate: validateBind }] = useForm({
    schemas: bindFormSchema,
    showActionButtonGroup: false,
    labelCol: {
      xs: { span: 24 },
      sm: { span: 6 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 18 },
    },
    baseColProps: { span: 24 },
    labelWidth: 100,
  });

  const pagination = reactive({
    current: 1,
    pageSize: DEFAULT_PAGE_SIZE,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条记录`,
  });

  const columns = [
    {
      title: '机柜名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '机柜编号',
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: '房间',
      dataIndex: 'roomName',
      key: 'roomName',
      width: 200,
    },
    {
      title: '总U位',
      dataIndex: 'totalU',
      key: 'totalU',
      width: 100,
      align: 'center',
    },
    {
      title: '使用情况',
      key: 'usage',
      width: 200,
    },
    {
      title: '已使用U位',
      dataIndex: 'useU',
      key: 'useU',
      width: 120,
      align: 'center',
    },
    {
      title: '未使用U位',
      dataIndex: 'unuseU',
      key: 'unuseU',
      width: 160,
      align: 'center',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 260,
      fixed: 'right',
    },
  ];

  const calculateUsagePercent = (record: AssetCabinetModel): number => {
    const total = parseInt(record.totalU) || 0;
    const used = parseInt(record.useU) || 0;
    if (total === 0) return 0;
    return Math.round((used / total) * 100);
  };

  const getUsageStatus = (record: AssetCabinetModel): string => {
    const percent = calculateUsagePercent(record);
    if (percent >= 90) return 'exception';
    if (percent >= 70) return 'active';
    return 'success';
  };

  // 获取已使用U位的颜色
  const getUsedUColor = (record: AssetCabinetModel): string => {
    const percent = calculateUsagePercent(record);
    if (percent >= 90) return 'red';
    if (percent >= 70) return 'orange';
    if (percent >= 40) return 'blue';
    return 'cyan';
  };

  // 解析并格式化未使用U位字符串，返回可用U位数量
  const getUnusedUCount = (record: AssetCabinetModel): number => {
    if (!record.unuseU) return 0;

    // 清理字符串：移除所有空格，统一分隔符为逗号
    const cleanStr = record.unuseU
      .toString()
      .replace(/\s+/g, '') // 移除所有空格
      .replace(/[.，、]/g, ',') // 将各种分隔符统一为逗号
      .replace(/,+/g, ',') // 合并多个连续逗号
      .replace(/^,|,$/g, ''); // 移除首尾逗号

    if (!cleanStr) return 0;

    // 分割并过滤出有效数字
    const numbers = cleanStr
      .split(',')
      .map((s) => s.trim())
      .filter((s) => s && /^\d+$/.test(s))
      .map((s) => parseInt(s, 10))
      .filter((n) => !isNaN(n) && n > 0);

    return numbers.length;
  };

  // 格式化未使用U位的详细信息用于tooltip显示
  const formatUnusedUTooltip = (unuseU: string | undefined): string => {
    if (!unuseU) return '暂无可用U位';

    // 清理字符串
    const cleanStr = unuseU
      .toString()
      .replace(/\s+/g, '')
      .replace(/[.，、]/g, ',')
      .replace(/,+/g, ',')
      .replace(/^,|,$/g, '');

    if (!cleanStr) return '暂无可用U位';

    // 解析并排序数字
    const numbers = cleanStr
      .split(',')
      .map((s) => s.trim())
      .filter((s) => s && /^\d+$/.test(s))
      .map((s) => parseInt(s, 10))
      .filter((n) => !isNaN(n) && n > 0)
      .sort((a, b) => a - b);

    if (numbers.length === 0) return '暂无可用U位';

    // 将连续数字合并为范围显示
    const ranges: string[] = [];
    let start = numbers[0];
    let end = numbers[0];

    for (let i = 1; i < numbers.length; i++) {
      if (numbers[i] === end + 1) {
        end = numbers[i];
      } else {
        ranges.push(start === end ? `${start}` : `${start}-${end}`);
        start = end = numbers[i];
      }
    }
    ranges.push(start === end ? `${start}` : `${start}-${end}`);

    const rangeStr = ranges.join(', ');
    return `可用U位: ${rangeStr} (共${numbers.length}个)`;
  };

  // 显示机柜详情弹窗
  const showCabinetDetail = async (record: ExtendedAssetCabinetModel): Promise<void> => {
    currentCabinetDetail.value = record;
    cabinetDetailVisible.value = true;
    cabinetDetailLoading.value = true;

    try {
      // 获取机柜绑定的设备信息
      const devices = await getCabinetDevicesByCabinetId(record.id);
      cabinetDevices.value = devices;
    } catch (error) {
      console.error('获取机柜设备信息失败:', error);
      message.error('获取设备信息失败');
      cabinetDevices.value = [];
    } finally {
      cabinetDetailLoading.value = false;
    }
  };

  // 关闭机柜详情弹窗
  const handleCabinetDetailCancel = (): void => {
    cabinetDetailVisible.value = false;
    currentCabinetDetail.value = null;
    cabinetDevices.value = [];
  };

  // 获取U位槽的CSS类
  const getUSlotClass = (uNum: number, record: AssetCabinetModel): string => {
    const usedUPositions = getUsedUPositions();
    const availableUPositions = getAvailableUPositions(record);

    if (usedUPositions.includes(uNum)) {
      return 'used';
    } else if (availableUPositions.includes(uNum)) {
      return 'available';
    }
    return 'unknown';
  };

  // 获取U位槽的状态文本
  const getUSlotStatus = (uNum: number, record: AssetCabinetModel): string => {
    const usedUPositions = getUsedUPositions();
    const availableUPositions = getAvailableUPositions(record);

    if (usedUPositions.includes(uNum)) {
      // 查找占用此U位的设备
      const device = cabinetDevices.value.find((device: CabinetDeviceInfo) => {
        const startU = device.startU;
        const endU = device.endU || startU + (device.uSize || 1) - 1;
        return uNum >= startU && uNum <= endU;
      });
      return device ? device.deviceName : '已用';
    } else if (availableUPositions.includes(uNum)) {
      return '可用';
    }
    return '未知';
  };

  // 获取已使用的U位位置数组（基于实际设备绑定信息）
  const getUsedUPositions = (): number[] => {
    const positions: number[] = [];
    cabinetDevices.value.forEach((device: CabinetDeviceInfo) => {
      const startU = device.startU;
      const endU = device.endU || startU + (device.uSize || 1) - 1;
      for (let i = startU; i <= endU; i++) {
        positions.push(i);
      }
    });
    return positions;
  };

  // 获取可用的U位位置数组
  const getAvailableUPositions = (record: AssetCabinetModel): number[] => {
    if (!record.unuseU) return [];

    // 清理并解析未使用U位字符串
    const cleanStr = record.unuseU
      .toString()
      .replace(/\s+/g, '')
      .replace(/[.，、]/g, ',')
      .replace(/,+/g, ',')
      .replace(/^,|,$/g, '');

    if (!cleanStr) return [];

    // 解析数字
    const numbers = cleanStr
      .split(',')
      .map((s) => s.trim())
      .filter((s) => s && /^\d+$/.test(s))
      .map((s) => parseInt(s, 10))
      .filter((n) => !isNaN(n) && n > 0);

    return numbers;
  };

  // 设备块类型定义
  interface DeviceBlock {
    startU: number;
    endU: number;
    device: CabinetDeviceInfo | null;
    type: 'used' | 'available' | 'unknown';
    height: number;
  }

  // 生成合并后的设备块列表
  const generateDeviceBlocks = (record: AssetCabinetModel): DeviceBlock[] => {
    const totalU = parseInt(record.totalU);
    const availableUPositions = getAvailableUPositions(record);
    const blocks: DeviceBlock[] = [];

    // 创建一个U位状态映射
    const uPositionMap: { [key: number]: { device: CabinetDeviceInfo | null; type: 'used' | 'available' | 'unknown' } } = {};

    // 初始化所有U位为未知状态
    for (let i = 1; i <= totalU; i++) {
      uPositionMap[i] = { device: null, type: 'unknown' };
    }

    // 标记可用U位
    availableUPositions.forEach((u) => {
      if (uPositionMap[u]) {
        uPositionMap[u].type = 'available';
      }
    });

    // 标记设备占用的U位
    cabinetDevices.value.forEach((device: CabinetDeviceInfo) => {
      const startU = device.startU;
      const endU = device.endU || startU + (device.uSize || 1) - 1;
      for (let i = startU; i <= endU; i++) {
        if (uPositionMap[i]) {
          uPositionMap[i] = { device, type: 'used' };
        }
      }
    });

    // 只合并已绑定设备的连续U位，其他U位保持单独显示
    let currentBlock: DeviceBlock | null = null;

    for (let i = totalU; i >= 1; i--) {
      // 从上到下（高U位到低U位）
      const current = uPositionMap[i];

      if (!currentBlock) {
        // 开始新块
        currentBlock = {
          startU: i,
          endU: i,
          device: current.device,
          type: current.type,
          height: 1,
        };
      } else {
        // 只合并相同设备的连续U位，其他类型不合并
        const canMerge = current.type === 'used' && currentBlock.type === 'used' && current.device?.deviceId === currentBlock.device?.deviceId;

        if (canMerge) {
          // 合并到当前块
          currentBlock.startU = i;
          currentBlock.height++;
        } else {
          // 完成当前块，开始新块
          blocks.push(currentBlock);
          currentBlock = {
            startU: i,
            endU: i,
            device: current.device,
            type: current.type,
            height: 1,
          };
        }
      }
    }

    // 添加最后一个块
    if (currentBlock) {
      blocks.push(currentBlock);
    }

    return blocks;
  };

  // 在设备块上解绑设备
  const handleUnbindDeviceFromBlock = async (device: CabinetDeviceInfo): Promise<void> => {
    if (!currentCabinetDetail.value) return;

    // 检查设备ID字段
    if (!device.deviceId) {
      console.error('设备ID为空或未定义:', device);
      message.error('设备ID无效，无法解绑');
      return;
    }

    try {
      await unbindCabinetDevices({
        cabinetId: currentCabinetDetail.value.id,
        deviceId: device.deviceId.toString(),
      });
      message.success('设备解绑成功');

      // 重新获取设备信息
      const devices = await getCabinetDevicesByCabinetId(currentCabinetDetail.value.id);
      cabinetDevices.value = devices;

      // 重新获取机柜详细信息，更新unuseU字段
      try {
        const updatedCabinet = await getAssetCabinetById(currentCabinetDetail.value.id);
        const room = roomOptions.value.find((room: { label: string; value: number }) => room.value === updatedCabinet.roomId);
        currentCabinetDetail.value = {
          ...updatedCabinet,
          roomName: room ? room.label : `房间ID: ${updatedCabinet.roomId}`,
        };
      } catch (cabinetError) {
        console.error('获取机柜详细信息失败:', cabinetError);
        // 即使获取机柜信息失败，也不影响设备解绑的成功状态
      }

      // 刷新列表数据
      loadData();
    } catch (error) {
      console.error('解绑设备失败:', error);
      message.error('解绑设备失败');
    }
  };

  const filterOption = (input: string, option: any): boolean => {
    return option?.label?.toLowerCase().includes(input.toLowerCase());
  };

  const buildParams = (): AssetCabinetParams => {
    return {
      pageNo: pagination.current,
      pageSize: pagination.pageSize,
      name: searchForm.name || undefined,
      roomId: searchForm.roomId || undefined,
    };
  };

  const resetSearchForm = (): void => {
    searchForm.name = '';
    searchForm.roomId = undefined;
  };

  const loadRoomOptions = async (): Promise<void> => {
    try {
      const result = await getRoomList({ current: 1, size: 1000 });
      roomOptions.value = result.records.map((room: RoomModel) => ({
        label: `${room.roomName} (${room.floors}楼)`,
        value: room.id,
      }));
    } catch (error) {
      console.error('加载房间数据失败:', error);
      message.error('加载房间数据失败');
    }
  };

  // 加载设备选项数据
  const loadDeviceOptions = async (): Promise<void> => {
    try {
      const result = await getAssetDeviceList({ pageNo: 1, pageSize: 1000 });
      deviceOptions.value = result.records.map((device: AssetDeviceModel) => ({
        label: `${device.deviceName} (${device.models})`,
        value: device.id,
      }));
    } catch (error) {
      console.error('加载设备数据失败:', error);
      message.error('加载设备数据失败');
    }
  };

  const loadData = async (): Promise<void> => {
    try {
      loading.value = true;
      const params = buildParams();
      const result = await getAssetCabinetList(params);

      tableData.value = result.records.map((cabinet: AssetCabinetModel): ExtendedAssetCabinetModel => {
        const room = roomOptions.value.find((room: { label: string; value: number }) => room.value === cabinet.roomId);
        return {
          ...cabinet,
          roomName: room ? room.label : `房间ID: ${cabinet.roomId}`,
        };
      });
      pagination.total = result.total;
    } catch (error) {
      console.error('加载机柜数据失败:', error);
      message.error('加载数据失败');
    } finally {
      loading.value = false;
    }
  };

  const handleSearch = (): void => {
    pagination.current = 1;
    loadData();
  };

  const handleReset = (): void => {
    resetSearchForm();
    pagination.current = 1;
    loadData();
  };

  const handleRefresh = (): void => {
    loadData();
    message.success('数据已刷新');
  };

  const handleTableChange = (pag: any): void => {
    pagination.current = pag.current;
    pagination.pageSize = pag.pageSize;
    loadData();
  };

  const onSelectChange = (keys: number[]): void => {
    selectedRowKeys.value = keys;
  };

  const handleAdd = async (): Promise<void> => {
    isEdit.value = false;
    modalVisible.value = true;
    resetFields();
    if (roomOptions.value.length === 0) {
      await loadRoomOptions();
    }
  };

  const handleEdit = async (record: ExtendedAssetCabinetModel): Promise<void> => {
    isEdit.value = true;
    modalVisible.value = true;
    if (roomOptions.value.length === 0) {
      await loadRoomOptions();
    }
    try {
      await safeSetFieldsValue(setFieldsValue, {
        ...record,
        totalU: parseInt(record.totalU),
      });
    } catch (error) {
      console.error('设置表单值失败:', error);
      message.error('加载编辑数据失败');
    }
  };

  const handleSubmit = async (): Promise<void> => {
    try {
      const values = await validate();
      submitLoading.value = true;

      const submitData = {
        ...values,
        totalU: values.totalU.toString(),
        // 新增时不发送useU和unuseU，让后端处理
        // 编辑时保留原有的useU和unuseU值
      };

      if (isEdit.value) {
        const updateData: AssetCabinetModel = {
          ...submitData,
          id: typeof values.id === 'string' ? parseInt(values.id, 10) : values.id,
        };

        if (!updateData.id || isNaN(updateData.id)) {
          throw new Error('无效的机柜ID');
        }

        await updateAssetCabinet(updateData);
      } else {
        const { id, ...addData } = submitData;
        await addAssetCabinet(addData);
      }

      message.success(isEdit.value ? '修改成功' : '新增成功');
      modalVisible.value = false;
      loadData();
    } catch (error) {
      console.error('提交失败:', error);
      // 由全局请求处理器负责错误提示
      // const errorMessage = error instanceof Error ? error.message : '操作失败';
      // message.error(errorMessage);
    } finally {
      submitLoading.value = false;
    }
  };

  const handleCancel = (): void => {
    modalVisible.value = false;
    resetFields();
  };

  const handleDelete = async (ids: number[]): Promise<void> => {
    if (!ids || ids.length === 0) {
      message.warning('请选择要删除的机柜');
      return;
    }

    try {
      await deleteAssetCabinet(ids);
      message.success('删除成功');
      selectedRowKeys.value = [];
      loadData();
    } catch (error) {
      console.error('删除失败:', error);
      // 由全局请求处理器负责错误提示
      // const errorMessage = error instanceof Error ? error.message : '删除失败';
      // message.error(errorMessage);
    }
  };

  // 绑定设备相关方法
  const handleBindDevice = async (record: ExtendedAssetCabinetModel): Promise<void> => {
    currentCabinet.value = record;

    // 确保设备选项已加载
    if (deviceOptions.value.length === 0) {
      await loadDeviceOptions();
    }

    // 打开弹窗，表单初始化将在watch中处理
    bindModalVisible.value = true;
  };

  const handleBindSubmit = async (): Promise<void> => {
    try {
      const values = await validateBind();
      bindSubmitLoading.value = true;

      const bindData: CabinetBindDeviceParams = {
        assDeviceId: values.assDeviceId,
        cabinetId: currentCabinet.value!.id,
        startU: values.startU,
      };

      await bindDeviceToCabinet(bindData);
      message.success('设备绑定成功');
      bindModalVisible.value = false;
      loadData(); // 刷新列表以更新使用情况
    } catch (error) {
      console.error('绑定失败:', error);
      // 由全局请求处理器负责错误提示
      // const errorMessage = error instanceof Error ? error.message : '绑定失败';
      // message.error(errorMessage);
    } finally {
      bindSubmitLoading.value = false;
    }
  };

  const handleBindCancel = (): void => {
    bindModalVisible.value = false;
    resetBindFields();
    currentCabinet.value = null;
  };

  // 监听绑定弹窗状态，在弹窗打开后初始化表单
  watch(bindModalVisible, async (newVal) => {
    if (newVal && currentCabinet.value) {
      // 等待弹窗完全渲染
      await nextTick();

      // 延迟一点时间确保表单完全初始化
      setTimeout(async () => {
        try {
          resetBindFields();
          await setBindFieldsValue({
            cabinetName: currentCabinet.value!.name,
          });
        } catch (error) {
          console.warn('表单初始化失败:', error);
          // 再次尝试
          setTimeout(async () => {
            try {
              resetBindFields();
              await setBindFieldsValue({
                cabinetName: currentCabinet.value!.name,
              });
            } catch (retryError) {
              console.error('表单初始化重试失败:', retryError);
            }
          }, 200);
        }
      }, 50);
    }
  });

  onMounted(async () => {
    await loadRoomOptions();
    loadData();
  });
</script>
